* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

.app {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: background-image 0.5s ease-in-out;
  overflow: hidden;
  padding: 1vh;
  box-sizing: border-box;
}

.container {
  width: 96vw;
  height: 96vh;
  padding: 1vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1vh;
  overflow: hidden;
}

.search-form {
  display: flex;
  gap: 1vw;
  justify-content: center;
  width: 100%;
  max-width: 70vw;
  margin: 0 auto;
  margin-bottom: 1vh;
}

.search-input {
  flex: 1;
  padding: 2vh 3vw;
  border: none;
  border-radius: 30px;
  backdrop-filter: blur(10px);
  color: white;
  font-size: clamp(14px, 1.2vw, 20px);
  outline: none;
  transition: all 0.3s ease;
}

/* Day mode search input */
.day-mode .search-input {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.day-mode .search-input:focus {
  background: rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.02);
}

/* Night mode search input */
.night-mode .search-input {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.night-mode .search-input:focus {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.02);
}

/* Default search input (when no weather data) */
.search-input {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-button {
  padding: 2vh 4vw;
  border: none;
  border-radius: 30px;
  backdrop-filter: blur(10px);
  color: white;
  font-size: clamp(14px, 1.2vw, 20px);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* Day mode search button */
.day-mode .search-button {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.day-mode .search-button:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

/* Night mode search button */
.night-mode .search-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.night-mode .search-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Default search button (when no weather data) */
.search-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.location-button {
  padding: 2vh;
  border: none;
  border-radius: 50%;
  backdrop-filter: blur(10px);
  color: white;
  font-size: clamp(16px, 1.5vw, 24px);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 8vh;
  height: 8vh;
}

/* Day mode location button */
.day-mode .location-button {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.day-mode .location-button:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

/* Night mode location button */
.night-mode .location-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.night-mode .location-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Default location button (when no weather data) */
.location-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.location-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error {
  background: rgba(255, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 0, 0, 0.3);
  color: white;
  padding: 2vh;
  border-radius: 0;
  text-align: center;
  width: 80%;
  max-width: 80vw;
  margin: 0 auto;
  font-size: clamp(12px, 1vw, 16px);
}

.main-content {
  backdrop-filter: blur(20px);
  border-radius: 0;
  padding: 2vh 2vw;
  color: white;
  animation: slideUp 0.5s ease-out;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2vw;
  width: 70vw;
  height: 70vh;
  margin: 0 auto;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Day mode - darker glassmorphism for bright backgrounds */
.main-content.day-mode {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Night mode - lighter glassmorphism for dark backgrounds */
.main-content.night-mode {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.weather-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  overflow: hidden;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2vh;
  flex-wrap: wrap;
  gap: 2vw;
}

.weather-header h2 {
  font-size: clamp(16px, 1.5vw, 24px);
  font-weight: 400;
}

.unit-toggle {
  padding: 1vh 2vw;
  border: none;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: clamp(12px, 1vw, 16px);
}

/* Day mode unit toggle */
.day-mode .unit-toggle {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.day-mode .unit-toggle:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(1.05);
}

/* Night mode unit toggle */
.night-mode .unit-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.night-mode .unit-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Default unit toggle (when no weather data) */
.unit-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.unit-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.weather-main {
  text-align: center;
  margin-bottom: 2vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5vh;
}

.weather-icon-main {
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-weather-emoji {
  font-size: 6vh;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  line-height: 1;
}

.temperature {
  font-size: clamp(24px, 4vw, 48px);
  font-weight: 200;
  margin-bottom: 1vh;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.weather-description {
  font-size: clamp(14px, 1.5vw, 20px);
  text-transform: capitalize;
  opacity: 0.9;
}

.weather-separator {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
  margin: 1.5vh 0;
  border: none;
}

.weather-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5vh;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8vh 1vw;
  backdrop-filter: blur(10px);
  border-radius: 0;
  transition: all 0.3s ease;
}

/* Day mode detail items */
.day-mode .detail-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.day-mode .detail-item:hover {
  background: rgba(0, 0, 0, 0.4);
}

/* Night mode detail items */
.night-mode .detail-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.night-mode .detail-item:hover {
  background: rgba(255, 255, 255, 0.2);
}



.detail-item span:first-child {
  opacity: 0.8;
  font-size: clamp(11px, 1vw, 14px);
}

.detail-item span:last-child {
  font-weight: 500;
  font-size: clamp(11px, 1vw, 14px);
}

/* Forecast Styles */
.forecast-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.forecast-title {
  text-align: center;
  font-size: clamp(16px, 1.5vw, 24px);
  font-weight: 400;
  margin-bottom: 1vh;
  opacity: 0.9;
}

.forecast-grid {
  display: flex;
  flex-direction: column;
  gap: 0.3vh;
  flex: 1;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.forecast-item {
  margin-top:1.3vh;
  display: grid;
  grid-template-columns: 1fr auto auto 2fr;
  align-items: center;
  padding: 1.5vh 1.5vw;
  backdrop-filter: blur(10px);
  border-radius: 0;
  transition: all 0.3s ease;
  gap: 1.5vw;
  width: 100%;
  flex: 1;
  min-height: calc((100% - 6 * 0.8vh - 6 * 1.3vh) / 7);
}

/* Day mode forecast items */
.day-mode .forecast-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.day-mode .forecast-item:hover {
  background: rgba(0, 0, 0, 0.4);
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

/* Night mode forecast items */
.night-mode .forecast-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.night-mode .forecast-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.forecast-day {
  font-size: clamp(12px, 1.2vw, 18px);
  font-weight: 500;
  opacity: 0.9;
  min-width: 15%;
}

.forecast-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.forecast-emoji {
  font-size: 3vh;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  line-height: 1;
}

.forecast-temps {
  display: flex;
  gap: 1vw;
  font-weight: 500;
  align-items: center;
  min-width: 15%;
  justify-content: center;
}

.temp-high {
  color: white;
  font-size: clamp(12px, 1.2vw, 18px);
}

.temp-low {
  color: rgba(255, 255, 255, 0.7);
  font-size: clamp(12px, 1.2vw, 18px);
}

.forecast-desc {
  font-size: clamp(11px, 1vw, 14px);
  opacity: 0.8;
  text-transform: capitalize;
  line-height: 1.2;
  text-align: left;
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 2vh;
    width: 96vw;
    padding: 1.5vh 2vw;
    height: 85vh;
  }

  .forecast-grid {
    gap: 0.6vh;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1vh;
    height: 98vh;
  }

  .search-form {
    flex-direction: column;
    width: 100%;
    margin-bottom: 2vh;
    gap: 2vh;
  }

  .search-input {
    padding: 2vh 4vw;
    font-size: clamp(16px, 4vw, 24px);
  }

  .search-button {
    padding: 2vh 6vw;
    font-size: clamp(16px, 4vw, 24px);
  }

  .location-button {
    padding: 2vh;
    font-size: 5vw;
    width: 12vw;
    height: 12vw;
    min-width: 50px;
    min-height: 50px;
  }

  .main-content {
    grid-template-columns: 1fr;
    gap: 2vh;
    padding: 2vh 3vw;
    height: 85vh;
  }

  .weather-header {
    flex-direction: column;
    text-align: center;
    margin-bottom: 2vh;
  }

  .weather-header h2 {
    font-size: clamp(18px, 4vw, 28px);
  }

  .temperature {
    font-size: clamp(32px, 8vw, 64px);
  }

  .main-weather-emoji {
    font-size: 12vh;
  }

  .weather-description {
    font-size: clamp(16px, 3vw, 24px);
  }

  .weather-details {
    grid-template-columns: 1fr;
    gap: 1vh;
  }

  .detail-item span:first-child,
  .detail-item span:last-child {
    font-size: clamp(14px, 3vw, 20px);
  }

  .forecast-item {
    grid-template-columns: 1fr auto auto 1.5fr;
    padding: 2vh 3vw;
    gap: 3vw;
  }

  .forecast-emoji {
    font-size: 6vh;
  }

  .forecast-day {
    font-size: clamp(14px, 3vw, 20px);
    min-width: 20%;
  }

  .temp-high, .temp-low {
    font-size: clamp(14px, 3vw, 20px);
  }

  .forecast-desc {
    font-size: clamp(12px, 2.5vw, 18px);
  }

  .forecast-temps {
    min-width: 20%;
  }

  .unit-toggle {
    font-size: clamp(14px, 3vw, 20px);
  }
}
