<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>Weather App - Real-time Weather Forecast with Dynamic Design</title>
    <meta name="title" content="Weather App - Real-time Weather Forecast with Dynamic Design" />
    <meta name="description" content="Modern weather app with glassmorphism design, real-time forecasts, and dynamic backgrounds. Get current weather and 7-day forecasts for any location worldwide." />
    <meta name="keywords" content="weather, forecast, weather app, glassmorphism, real-time weather, temperature, climate" />
    <meta name="author" content="T14X" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://weather-t14x.netlify.app/" />
    <meta property="og:title" content="Weather App - Real-time Weather Forecast" />
    <meta property="og:description" content="Modern weather app with glassmorphism design, real-time forecasts, and dynamic backgrounds." />
    <meta property="og:image" content="https://weather-t14x.netlify.app/sunny.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://weather-t14x.netlify.app/" />
    <meta property="twitter:title" content="Weather App - Real-time Weather Forecast" />
    <meta property="twitter:description" content="Modern weather app with glassmorphism design, real-time forecasts, and dynamic backgrounds." />
    <meta property="twitter:image" content="https://weather-t14x.netlify.app/sunny.jpg" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#1e40af" />
    <meta name="msapplication-TileColor" content="#1e40af" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
